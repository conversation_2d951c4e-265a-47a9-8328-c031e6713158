"""
    Main module for amazon_scraper.
"""

import logging
import click
from typing import Optional
import concurrent.futures
from functools import partial

from amazon_scraper.collector import AmazonDataCollector
from amazon_scraper.config import AMAZON_CATEGORIES

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def scrape_category(collector: AmazonDataCollector, category_info: tuple) -> None:
    category_name, category_url = category_info
    logger.info(f"Scraping category: {category_name}")
    collector.collect_amazon_product_data(category_url)

@click.command()
@click.option(
    "--url",
    help="The url of the page for which to return Amazon product data for.",
    required=False,
)
@click.option(
    "--category",
    help="Category name to scrape from predefined categories.",
    required=False,
)
@click.option(
    "--all",
    "scrape_all",
    is_flag=True,
    help="Scrape all predefined categories.",
    required=False,
)
@click.option(
    "--workers",
    default=3,
    help="Number of parallel workers for scraping (default: 3)",
    required=False,
)
def scrape_amazon(url: Optional[str], category: Optional[str], scrape_all: bool, workers: int) -> None:
    collector = AmazonDataCollector()
    
    if url:
        collector.collect_amazon_product_data(url)
    elif category:
        if category not in AMAZON_CATEGORIES:
            available_categories = ", ".join(AMAZON_CATEGORIES.keys())
            logger.error(f"Category '{category}' not found. Available categories: {available_categories}")
            return
        collector.collect_amazon_product_data(AMAZON_CATEGORIES[category])
    elif scrape_all:
        with concurrent.futures.ThreadPoolExecutor(max_workers=workers) as executor:
            scrape_func = partial(scrape_category, collector)
            executor.map(scrape_func, AMAZON_CATEGORIES.items())
    else:
        logger.error("Please provide either --url, --category, or --all flag")

if __name__ == "__main__":
    scrape_amazon()