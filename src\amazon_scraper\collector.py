"""
    Main module for collecting Amazon product data.
"""

import logging
import csv
from typing import List
from urllib.parse import parse_qs, urlparse
import time
import os

import pandas as pd

from amazon_scraper.models import Product
from amazon_scraper.scraper import AmazonScraper
from amazon_scraper.config import AMAZON_CATEGORIES


def _get_search_term(url: str) -> str:
    """Extract search term from Amazon URL"""
    parsed_url = urlparse(url)
    query_params = parse_qs(parsed_url.query)

    # Get search term from 'k' parameter
    search_term = query_params.get('k', ['products'])[0]
    # Clean up the search term
    search_term = search_term.lower().replace(' ', '_')
    return f"amazon_{search_term}.csv"


def _get_category_name(url: str) -> str:
    """Get category name from config based on URL"""
    for category, category_url in AMAZON_CATEGORIES.items():
        if url == category_url:
            return category
    return "unknown_category"


class AmazonDataCollector:
    """Data collector class for Amazon pages"""

    def __init__(
        self,
        output_file: str | None = None,
        logger: logging.Logger | None = None,
        scraper: AmazonScraper | None = None,
    ) -> None:
        # Use provided scraper or create a new one (fast mode by default)
        self._scraper = scraper if scraper else AmazonScraper(use_proxy=False)
        self._logger = logger if logger else logging.getLogger(__name__)
        # We'll set the output file when we get the URL
        self._output_file = None

    def _save_to_csv(self, products: List[Product], filename: str) -> None:
        """Saves the products to a CSV file"""
        try:
            df = pd.DataFrame([p.__dict__ for p in products])
            # Ensure the data directory exists
            os.makedirs("data", exist_ok=True)
            filepath = os.path.join("data", filename)
            df.to_csv(filepath, index=False)
        except Exception as e:
            self._logger.error(f"Error saving to CSV: {str(e)}")
            raise

    def _get_category_from_url(self, url: str) -> str:
        """Extract category name from URL for file naming"""
        # Try to extract search term from URL
        if "k=" in url:
            search_term = url.split("k=")[1].split("&")[0].lower()
            return f"amazon_{search_term}"
        else:
            # Fallback to timestamp if no search term found
            return f"amazon_products_{int(time.time())}"

    def collect_amazon_product_data(self, url: str) -> None:
        """
        Scrapes data from a given Amazon page and stores it into a CSV file.

        Args:
            url (str): The URL of the Amazon page for which to scrape data.
        """
        # Get category name from config
        category_name = _get_category_name(url)
        self._output_file = f"{category_name}.csv"

        self._logger.info(f"Getting Amazon product data for url {url}..")
        try:
            # Ultra-fast mode: scrape minimal products for maximum speed
            products = self._scraper.scrape_amazon_page(url, max_pages=1)
        except Exception:
            self._logger.exception(f"Error when scraping Amazon for url {url}.")
            return

        if not products:
            self._logger.info("No products found for given Amazon page.")
            return

        # Use category name from config for filename
        self._save_to_csv(products, self._output_file)
        self._logger.info(f"Saved {len(products)} products to {self._output_file}")
